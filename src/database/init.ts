import { dbConnection } from './connection';
import { databaseSchema } from './schema';
import { config } from '@/utils/config';

export async function initializeDatabase(): Promise<void> {
  try {
    console.log('Starting database initialization...');
    
    // Validate configuration
    config.validateConfig();
    
    // Initialize database connection
    await dbConnection.initialize();
    
    // Check if database is ready
    if (!dbConnection.isReady()) {
      throw new Error('Database connection is not ready');
    }
    
    // Initialize schema
    await databaseSchema.initializeSchema();
    
    // Validate schema
    const isValid = await databaseSchema.validateSchema();
    if (!isValid) {
      throw new Error('Schema validation failed');
    }
    
    console.log('Database initialization completed successfully');
    
    // Log database statistics
    const stats = await dbConnection.getStats();
    console.log('Database statistics:', stats);
    
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
}

export async function resetDatabase(): Promise<void> {
  try {
    console.log('Resetting database...');
    
    await dbConnection.initialize();
    await databaseSchema.dropAllTables();
    await databaseSchema.initializeSchema();
    
    console.log('Database reset completed successfully');
  } catch (error) {
    console.error('Database reset failed:', error);
    throw error;
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  
  (async () => {
    try {
      switch (command) {
        case 'reset':
          await resetDatabase();
          break;
        default:
          await initializeDatabase();
          break;
      }
      process.exit(0);
    } catch (error) {
      console.error('Database operation failed:', error);
      process.exit(1);
    }
  })();
}