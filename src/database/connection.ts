import * as duckdb from 'duckdb';
import * as path from 'path';
import * as fs from 'fs/promises';
import { config } from '@/utils/config';
import { DatabaseError, QueryResult, ConnectionStatus } from '@/types';

export class DatabaseConnection {
  private static instance: DatabaseConnection;
  private db: duckdb.Database | null = null;
  private connection: duckdb.Connection | null = null;
  private isConnected = false;
  private connectionStatus: ConnectionStatus = {
    isConnected: false,
    connectionAttempts: 0,
  };

  private constructor() {}

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public async initialize(): Promise<void> {
    try {
      await this.connect();
      console.log('Database connection initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database connection:', error);
      throw new DatabaseError('Database initialization failed', 'INIT_ERROR', error);
    }
  }

  private async connect(): Promise<void> {
    try {
      this.connectionStatus.connectionAttempts++;

      const dbConfig = config.getDatabaseConfig();
      
      // Ensure data directory exists
      const dataDir = path.dirname(dbConfig.path);
      await fs.mkdir(dataDir, { recursive: true });

      // Create DuckDB database
      this.db = new duckdb.Database(dbConfig.path);
      
      // Get connection
      this.connection = this.db.connect();

      // Configure DuckDB settings
      await this.configureDatabase();

      // Test connection
      await this.testConnection();

      this.isConnected = true;
      this.connectionStatus.isConnected = true;
      this.connectionStatus.lastConnectionAt = new Date();
      this.connectionStatus.lastError = undefined;

    } catch (error) {
      this.isConnected = false;
      this.connectionStatus.isConnected = false;
      this.connectionStatus.lastError = error instanceof Error ? error.message : 'Unknown error';
      throw new DatabaseError('Failed to connect to database', 'CONNECTION_ERROR', error);
    }
  }

  private async configureDatabase(): Promise<void> {
    if (!this.connection) {
      throw new DatabaseError('No database connection available', 'NO_CONNECTION');
    }

    const dbConfig = config.getDatabaseConfig();

    try {
      // Configure DuckDB settings
      const settings = [
        'SET memory_limit = \'1GB\'',
        'SET threads = 4',
        'SET enable_progress_bar = false',
        'SET enable_print_progress_bar = false',
      ];

      // Enable WAL mode if configured
      if (dbConfig.enableWAL) {
        settings.push('PRAGMA journal_mode = WAL');
      }

      for (const setting of settings) {
        await this.executeQuery(setting);
      }

      // Install and load required extensions
      const extensions = [
        'httpfs',  // For reading from HTTP(S) sources
        'json',    // For JSON operations
        'fts',     // For full-text search (if available)
      ];

      for (const ext of extensions) {
        try {
          await this.executeQuery(`INSTALL ${ext}`);
          await this.executeQuery(`LOAD ${ext}`);
        } catch (error) {
          console.warn(`Failed to load extension ${ext}:`, error);
          // Continue with other extensions
        }
      }

    } catch (error) {
      console.warn('Failed to configure some database settings:', error);
      // Don't throw here as basic functionality should still work
    }
  }

  private async testConnection(): Promise<void> {
    if (!this.connection) {
      throw new DatabaseError('No database connection available', 'NO_CONNECTION');
    }

    try {
      await this.executeQuery('SELECT 1 as test');
    } catch (error) {
      throw new DatabaseError('Database connection test failed', 'CONNECTION_TEST_FAILED', error);
    }
  }

  public async executeQuery<T = any>(
    query: string, 
    params: any[] = []
  ): Promise<QueryResult<T>> {
    if (!this.connection || !this.isConnected) {
      throw new DatabaseError('Database not connected', 'NOT_CONNECTED');
    }

    return new Promise((resolve, reject) => {
      try {
        this.connection!.all(query, params, (err: Error | null, rows: any[]) => {
          if (err) {
            reject(new DatabaseError(`Query execution failed: ${err.message}`, 'QUERY_ERROR', err));
            return;
          }

          resolve({
            rows: rows || [],
            rowCount: rows ? rows.length : 0,
            command: query.trim().split(' ')[0].toUpperCase(),
          });
        });
      } catch (error) {
        reject(new DatabaseError('Query execution failed', 'QUERY_ERROR', error));
      }
    });
  }

  public async executeTransaction<T>(
    callback: (execute: (query: string, params?: any[]) => Promise<QueryResult>) => Promise<T>
  ): Promise<T> {
    if (!this.connection || !this.isConnected) {
      throw new DatabaseError('Database not connected', 'NOT_CONNECTED');
    }

    try {
      await this.executeQuery('BEGIN TRANSACTION');
      
      const executeInTransaction = async (query: string, params: any[] = []) => {
        return this.executeQuery(query, params);
      };

      const result = await callback(executeInTransaction);
      
      await this.executeQuery('COMMIT');
      return result;
      
    } catch (error) {
      try {
        await this.executeQuery('ROLLBACK');
      } catch (rollbackError) {
        console.error('Failed to rollback transaction:', rollbackError);
      }
      throw error;
    }
  }

  public async batch(queries: Array<{ query: string; params?: any[] }>): Promise<QueryResult[]> {
    const results: QueryResult[] = [];
    
    await this.executeTransaction(async (execute) => {
      for (const { query, params = [] } of queries) {
        const result = await execute(query, params);
        results.push(result);
      }
      return results;
    });

    return results;
  }

  public getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  public isReady(): boolean {
    return this.isConnected && this.connection !== null;
  }

  public async reconnect(): Promise<void> {
    if (this.connection) {
      try {
        this.connection.close();
      } catch (error) {
        console.warn('Error closing existing connection:', error);
      }
    }

    if (this.db) {
      try {
        this.db.close();
      } catch (error) {
        console.warn('Error closing existing database:', error);
      }
    }

    this.connection = null;
    this.db = null;
    this.isConnected = false;

    await this.connect();
  }

  public async close(): Promise<void> {
    try {
      if (this.connection) {
        this.connection.close();
        this.connection = null;
      }

      if (this.db) {
        this.db.close();
        this.db = null;
      }

      this.isConnected = false;
      this.connectionStatus.isConnected = false;
      
      console.log('Database connection closed successfully');
    } catch (error) {
      console.error('Error closing database connection:', error);
      throw new DatabaseError('Failed to close database connection', 'CLOSE_ERROR', error);
    }
  }

  // Health check method
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }
      
      await this.executeQuery('SELECT 1');
      return true;
    } catch (error) {
      console.warn('Database health check failed:', error);
      return false;
    }
  }

  // Get database statistics
  public async getStats(): Promise<any> {
    try {
      const [
        memoryUsage,
        tableCount,
        indexCount,
      ] = await Promise.all([
        this.executeQuery("SELECT * FROM pragma_database_size()"),
        this.executeQuery("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'main'"),
        this.executeQuery("SELECT COUNT(*) as count FROM information_schema.statistics"),
      ]);

      return {
        memoryUsage: memoryUsage.rows[0],
        tableCount: tableCount.rows[0]?.count || 0,
        indexCount: indexCount.rows[0]?.count || 0,
        isConnected: this.isConnected,
        connectionAttempts: this.connectionStatus.connectionAttempts,
        lastConnectionAt: this.connectionStatus.lastConnectionAt,
      };
    } catch (error) {
      console.warn('Failed to get database stats:', error);
      return {
        isConnected: this.isConnected,
        connectionAttempts: this.connectionStatus.connectionAttempts,
        lastConnectionAt: this.connectionStatus.lastConnectionAt,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

// Export singleton instance
export const dbConnection = DatabaseConnection.getInstance();