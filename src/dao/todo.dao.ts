import { BaseDAO } from './base.dao';
import { Todo, TodoStatus, TodoPriority, DatabaseError, FilterOptions, PaginationOptions } from '@/types';

export interface TodoFilterOptions extends FilterOptions {
  status?: TodoStatus;
  priority?: TodoPriority;
  category_id?: string;
  due_date_from?: Date;
  due_date_to?: Date;
  tags?: string[];
  search?: string;
  is_completed?: boolean;
  is_overdue?: boolean;
}

export class TodoDAO extends BaseDAO<Todo> {
  constructor() {
    super('todos');
  }

  public async findByUserId(
    userId: string, 
    pagination?: PaginationOptions,
    filters?: TodoFilterOptions
  ): Promise<{ data: Todo[]; total: number }> {
    let whereClause = 'WHERE user_id = $1 AND is_deleted = FALSE';
    let params: any[] = [userId];
    let paramIndex = 2;

    // Build additional filters
    if (filters) {
      if (filters.status) {
        whereClause += ` AND status = $${paramIndex}`;
        params.push(filters.status);
        paramIndex++;
      }

      if (filters.priority) {
        whereClause += ` AND priority = $${paramIndex}`;
        params.push(filters.priority);
        paramIndex++;
      }

      if (filters.category_id) {
        whereClause += ` AND category_id = $${paramIndex}`;
        params.push(filters.category_id);
        paramIndex++;
      }

      if (filters.due_date_from) {
        whereClause += ` AND due_date >= $${paramIndex}`;
        params.push(filters.due_date_from);
        paramIndex++;
      }

      if (filters.due_date_to) {
        whereClause += ` AND due_date <= $${paramIndex}`;
        params.push(filters.due_date_to);
        paramIndex++;
      }

      if (filters.tags && filters.tags.length > 0) {
        whereClause += ` AND tags && $${paramIndex}`;
        params.push(filters.tags);
        paramIndex++;
      }

      if (filters.search) {
        whereClause += ` AND (title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
        params.push(`%${filters.search}%`);
        paramIndex++;
      }

      if (filters.is_completed !== undefined) {
        if (filters.is_completed) {
          whereClause += ` AND status = 'completed'`;
        } else {
          whereClause += ` AND status != 'completed'`;
        }
      }

      if (filters.is_overdue) {
        whereClause += ` AND due_date < CURRENT_TIMESTAMP AND status NOT IN ('completed', 'cancelled')`;
      }
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM todos ${whereClause}`;
    const countResult = await this.executeQuery<{ total: number }>(countQuery, params);
    const total = countResult.rows[0]?.total || 0;

    // Build main query
    let query = `
      SELECT t.*, c.name as category_name, c.color as category_color
      FROM todos t
      LEFT JOIN categories c ON t.category_id = c.id
      ${whereClause}
    `;

    // Add ordering
    if (pagination?.sortBy) {
      const sortOrder = pagination.sortOrder || 'ASC';
      query += ` ORDER BY ${pagination.sortBy} ${sortOrder}`;
    } else {
      // Default ordering: by position, then by created_at
      query += ` ORDER BY position ASC, created_at DESC`;
    }

    // Add pagination
    if (pagination?.limit) {
      query += ` LIMIT $${paramIndex}`;
      params.push(pagination.limit);
      paramIndex++;

      if (pagination.page && pagination.page > 1) {
        const offset = (pagination.page - 1) * pagination.limit;
        query += ` OFFSET $${paramIndex}`;
        params.push(offset);
      }
    }

    const result = await this.executeQuery<Todo>(query, params);
    
    return {
      data: result.rows,
      total
    };
  }

  public async updateStatus(todoId: string, status: TodoStatus, userId: string): Promise<Todo> {
    const updates: any = { status, updated_at: new Date() };
    
    // Set completed_at when status is completed
