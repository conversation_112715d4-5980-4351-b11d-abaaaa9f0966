import { BaseDAO } from './base.dao';
import { User, UserProfile, DatabaseError } from '@/types';
import { databaseSchema } from '@/database/schema';

export class UserDAO extends BaseDAO<User> {
  constructor() {
    super('users');
  }

  public async findByUsername(username: string): Promise<User | null> {
    const result = await this.executeQuery<User>(
      'SELECT * FROM users WHERE username = $1',
      [username]
    );
    return result.rows[0] || null;
  }

  public async findByEmail(email: string): Promise<User | null> {
    const result = await this.executeQuery<User>(
      `SELECT u.* FROM users u 
       JOIN user_profiles up ON u.id = up.user_id 
       WHERE up.email = $1`,
      [email]
    );
    return result.rows[0] || null;
  }

  public async createWithProfile(
    userData: Omit<User, 'id' | 'created_at' | 'updated_at'>,
    profileData: Omit<UserProfile, 'user_id' | 'created_at' | 'updated_at'>
  ): Promise<{ user: User; profile: UserProfile }> {
    return await this.executeTransaction(async (execute) => {
      // Create user
      const userResult = await execute(
        `INSERT INTO users (username, password_hash, is_active, email_verified)
         VALUES ($1, $2, $3, $4)
         RETURNING *`,
        [userData.username, userData.password_hash, userData.is_active, userData.email_verified]
      );

      if (userResult.rows.length === 0) {
        throw new DatabaseError('Failed to create user', 'CREATE_ERROR');
      }

      const user = userResult.rows[0] as User;

      // Create user profile
      const profileResult = await execute(
        `INSERT INTO user_profiles (
          user_id, full_name, email, date_of_birth, profile_picture,
          theme_preference, notification_enabled, timezone, language_preference
         )
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
         RETURNING *`,
        [
          user.id,
          profileData.full_name,
          profileData.email,
          profileData.date_of_birth,
          profileData.profile_picture,
          profileData.theme_preference || 'light',
          profileData.notification_enabled !== false,
          profileData.timezone || 'UTC',
          profileData.language_preference || 'en'
        ]
      );

      if (profileResult.rows.length === 0) {
        throw new DatabaseError('Failed to create user profile', 'CREATE_ERROR');
      }

      const profile = profileResult.rows[0] as UserProfile;

      // Create default categories for the user
      await databaseSchema.createDefaultCategories(user.id, execute);

      return { user, profile };
    });
  }

  public async updateLastLogin(userId: string): Promise<void> {
    await this.executeQuery(
      'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
      [userId]
    );
  }

  public async activateUser(userId: string): Promise<User> {
    const result = await this.executeQuery<User>(
      'UPDATE users SET is_active = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *',
      [userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('User not found', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async deactivateUser(userId: string): Promise<User> {
    const result = await this.executeQuery<User>(
      'UPDATE users SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *',
      [userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('User not found', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async verifyEmail(userId: string): Promise<User> {
    const result = await this.executeQuery<User>(
      'UPDATE users SET email_verified = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *',
      [userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('User not found', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async updatePassword(userId: string, passwordHash: string): Promise<User> {
    const result = await this.executeQuery<User>(
      'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
      [passwordHash, userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('User not found', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async getActiveUsersCount(): Promise<number> {
    const result = await this.executeQuery<{ count: number }>(
      'SELECT COUNT(*) as count FROM users WHERE is_active = TRUE'
    );
    return result.rows[0]?.count || 0;
  }

  public async getRecentUsers(limit: number = 10): Promise<User[]> {
    const result = await this.executeQuery<User>(
      'SELECT * FROM users ORDER BY created_at DESC LIMIT $1',
      [limit]
    );
    return result.rows;
  }

  public async searchUsers(searchTerm: string, limit: number = 20): Promise<User[]> {
    const result = await this.executeQuery<User>(
      `SELECT u.* FROM users u
       JOIN user_profiles up ON u.id = up.user_id
       WHERE u.username ILIKE $1 
          OR up.full_name ILIKE $1 
          OR up.email ILIKE $1
       ORDER BY u.created_at DESC
       LIMIT $2`,
      [`%${searchTerm}%`, limit]
    );
    return result.rows;
  }

  public async getUserStats(userId: string): Promise<{
    totalTodos: number;
    completedTodos: number;
    pendingTodos: number;
    categoriesCount: number;
    lastActivity: Date | null;
  }> {
    const result = await this.executeQuery<{
      total_todos: number;
      completed_todos: number;
      pending_todos: number;
      categories_count: number;
      last_activity: Date;
    }>(
      `SELECT 
        COUNT(t.id) as total_todos,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_todos,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_todos,
        COUNT(DISTINCT c.id) as categories_count,
        MAX(GREATEST(t.updated_at, c.updated_at)) as last_activity
       FROM users u
       LEFT JOIN todos t ON u.id = t.user_id AND t.is_deleted = FALSE
       LEFT JOIN categories c ON u.id = c.user_id
       WHERE u.id = $1
       GROUP BY u.id`,
      [userId]
    );

    const row = result.rows[0];
    return {
      totalTodos: row?.total_todos || 0,
      completedTodos: row?.completed_todos || 0,
      pendingTodos: row?.pending_todos || 0,
      categoriesCount: row?.categories_count || 0,
      lastActivity: row?.last_activity || null,
    };
  }
}

export const userDAO = new UserDAO();