{"name": "duckdb", "main": "./lib/duckdb.js", "types": "./lib/duckdb.d.ts", "version": "1.3.2", "description": "DuckDB node.js API", "gypfile": true, "dependencies": {"@mapbox/node-pre-gyp": "^2.0.0", "node-addon-api": "^7.0.0", "node-gyp": "^9.3.0"}, "binary": {"module_name": "duckdb", "module_path": "./lib/binding/", "host": "https://npm.duckdb.org/duckdb"}, "scripts": {"install": "node-pre-gyp install --fallback-to-build", "pretest": "node test/support/createdb.js", "test": "mocha -R spec --timeout 480000 --expose-gc", "test-path": "mocha -R spec --timeout 480000 --expose-gc --exclude 'test/*.ts'", "pack": "node-pre-gyp package"}, "directories": {"lib": "lib", "test": "test"}, "devDependencies": {"@types/chai": "^4.3.4", "@types/chai-as-promised": "^7.1.5", "@types/fs-extra": "^11.0.1", "@types/mocha": "^10.0.0", "@types/node": "^18.11.0", "apache-arrow": "^9.0.0", "aws-sdk": "^2.790.0", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "fs-extra": "^11.1.1", "jsdoc3-parser": "^2.0.0", "mocha": "^8.3.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.0.0", "typescript": "^4.8.4"}, "repository": {"type": "git", "url": "git+https://github.com/duckdb/duckdb-node.git"}, "ts-node": {"require": ["tsconfig-paths/register"]}, "keywords": ["database", "sql", "duckdb"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/duckdb/duckdb-node/issues"}, "homepage": "https://www.duckdb.org"}