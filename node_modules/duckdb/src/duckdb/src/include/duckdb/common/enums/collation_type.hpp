//===----------------------------------------------------------------------===//
//                         DuckDB
//
// duckdb/common/enums/collation_type.hpp
//
//
//===----------------------------------------------------------------------===//

#pragma once

#include "duckdb/common/common.hpp"

namespace duckdb {

enum class CollationType { ALL_COLLATIONS, COMBINABLE_COLLATIONS };

} // namespace duckdb
