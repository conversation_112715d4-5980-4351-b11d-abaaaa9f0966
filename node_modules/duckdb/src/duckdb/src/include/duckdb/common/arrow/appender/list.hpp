//===----------------------------------------------------------------------===//
//                         DuckDB
//
// duckdb/common/arrow/appender/list.hpp
//
//
//===----------------------------------------------------------------------===//

#include "duckdb/common/arrow/appender/bool_data.hpp"
#include "duckdb/common/arrow/appender/enum_data.hpp"
#include "duckdb/common/arrow/appender/fixed_size_list_data.hpp"
#include "duckdb/common/arrow/appender/list_data.hpp"
#include "duckdb/common/arrow/appender/list_view_data.hpp"
#include "duckdb/common/arrow/appender/map_data.hpp"
#include "duckdb/common/arrow/appender/null_data.hpp"
#include "duckdb/common/arrow/appender/scalar_data.hpp"
#include "duckdb/common/arrow/appender/struct_data.hpp"
#include "duckdb/common/arrow/appender/union_data.hpp"
#include "duckdb/common/arrow/appender/varchar_data.hpp"
