//===----------------------------------------------------------------------===//
//                         DuckDB
//
// core_functions/scalar/struct_functions.hpp
//
//
//===----------------------------------------------------------------------===//
// This file is automatically generated by scripts/generate_functions.py
// Do not edit this file manually, your changes will be overwritten
//===----------------------------------------------------------------------===//

#pragma once

#include "duckdb/function/function_set.hpp"

namespace duckdb {

struct StructInsertFun {
	static constexpr const char *Name = "struct_insert";
	static constexpr const char *Parameters = "struct,any";
	static constexpr const char *Description = "Adds field(s)/value(s) to an existing STRUCT with the argument values. The entry name(s) will be the bound variable name(s)";
	static constexpr const char *Example = "struct_insert({'a': 1}, b := 2)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

} // namespace duckdb
