//===----------------------------------------------------------------------===//
//                         DuckDB
//
// core_functions/scalar/math_functions.hpp
//
//
//===----------------------------------------------------------------------===//
// This file is automatically generated by scripts/generate_functions.py
// Do not edit this file manually, your changes will be overwritten
//===----------------------------------------------------------------------===//

#pragma once

#include "duckdb/function/function_set.hpp"

namespace duckdb {

struct AbsOperatorFun {
	static constexpr const char *Name = "@";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Absolute value";
	static constexpr const char *Example = "abs(-17.4)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct AbsFun {
	using ALIAS = AbsOperatorFun;

	static constexpr const char *Name = "abs";
};

struct PowOperatorFun {
	static constexpr const char *Name = "**";
	static constexpr const char *Parameters = "x,y";
	static constexpr const char *Description = "Computes x to the power of y";
	static constexpr const char *Example = "pow(2, 3)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct PowFun {
	using ALIAS = PowOperatorFun;

	static constexpr const char *Name = "pow";
};

struct PowerFun {
	using ALIAS = PowOperatorFun;

	static constexpr const char *Name = "power";
};

struct PowOperatorFunAlias {
	using ALIAS = PowOperatorFun;

	static constexpr const char *Name = "^";
};

struct FactorialOperatorFun {
	static constexpr const char *Name = "!__postfix";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Factorial of x. Computes the product of the current integer and all integers below it";
	static constexpr const char *Example = "4!";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct FactorialFun {
	using ALIAS = FactorialOperatorFun;

	static constexpr const char *Name = "factorial";
};

struct AcosFun {
	static constexpr const char *Name = "acos";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the arccosine of x";
	static constexpr const char *Example = "acos(0.5)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct AsinFun {
	static constexpr const char *Name = "asin";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the arcsine of x";
	static constexpr const char *Example = "asin(0.5)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct AtanFun {
	static constexpr const char *Name = "atan";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the arctangent of x";
	static constexpr const char *Example = "atan(0.5)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct Atan2Fun {
	static constexpr const char *Name = "atan2";
	static constexpr const char *Parameters = "y,x";
	static constexpr const char *Description = "Computes the arctangent (y, x)";
	static constexpr const char *Example = "atan2(1.0, 0.0)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct BitCountFun {
	static constexpr const char *Name = "bit_count";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Returns the number of bits that are set";
	static constexpr const char *Example = "bit_count(31)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct CbrtFun {
	static constexpr const char *Name = "cbrt";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Returns the cube root of x";
	static constexpr const char *Example = "cbrt(8)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct CeilFun {
	static constexpr const char *Name = "ceil";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Rounds the number up";
	static constexpr const char *Example = "ceil(17.4)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct CeilingFun {
	using ALIAS = CeilFun;

	static constexpr const char *Name = "ceiling";
};

struct CosFun {
	static constexpr const char *Name = "cos";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the cos of x";
	static constexpr const char *Example = "cos(90)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct CotFun {
	static constexpr const char *Name = "cot";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the cotangent of x";
	static constexpr const char *Example = "cot(0.5)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct DegreesFun {
	static constexpr const char *Name = "degrees";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Converts radians to degrees";
	static constexpr const char *Example = "degrees(pi())";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct EvenFun {
	static constexpr const char *Name = "even";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Rounds x to next even number by rounding away from zero";
	static constexpr const char *Example = "even(2.9)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct ExpFun {
	static constexpr const char *Name = "exp";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes e to the power of x";
	static constexpr const char *Example = "exp(1)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct FloorFun {
	static constexpr const char *Name = "floor";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Rounds the number down";
	static constexpr const char *Example = "floor(17.4)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct IsFiniteFun {
	static constexpr const char *Name = "isfinite";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Returns true if the floating point value is finite, false otherwise";
	static constexpr const char *Example = "isfinite(5.5)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct IsInfiniteFun {
	static constexpr const char *Name = "isinf";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Returns true if the floating point value is infinite, false otherwise";
	static constexpr const char *Example = "isinf('Infinity'::float)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct IsNanFun {
	static constexpr const char *Name = "isnan";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Returns true if the floating point value is not a number, false otherwise";
	static constexpr const char *Example = "isnan('NaN'::FLOAT)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct GammaFun {
	static constexpr const char *Name = "gamma";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Interpolation of (x-1) factorial (so decimal inputs are allowed)";
	static constexpr const char *Example = "gamma(5.5)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct GreatestCommonDivisorFun {
	static constexpr const char *Name = "greatest_common_divisor";
	static constexpr const char *Parameters = "x,y";
	static constexpr const char *Description = "Computes the greatest common divisor of x and y";
	static constexpr const char *Example = "greatest_common_divisor(42, 57)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct GcdFun {
	using ALIAS = GreatestCommonDivisorFun;

	static constexpr const char *Name = "gcd";
};

struct LeastCommonMultipleFun {
	static constexpr const char *Name = "least_common_multiple";
	static constexpr const char *Parameters = "x,y";
	static constexpr const char *Description = "Computes the least common multiple of x and y";
	static constexpr const char *Example = "least_common_multiple(42, 57)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct LcmFun {
	using ALIAS = LeastCommonMultipleFun;

	static constexpr const char *Name = "lcm";
};

struct LogGammaFun {
	static constexpr const char *Name = "lgamma";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the log of the gamma function";
	static constexpr const char *Example = "lgamma(2)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct LnFun {
	static constexpr const char *Name = "ln";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the natural logarithm of x";
	static constexpr const char *Example = "ln(2)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct Log2Fun {
	static constexpr const char *Name = "log2";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the 2-log of x";
	static constexpr const char *Example = "log2(8)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct Log10Fun {
	static constexpr const char *Name = "log10";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the 10-log of x";
	static constexpr const char *Example = "log10(1000)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct LogFun {
	static constexpr const char *Name = "log";
	static constexpr const char *Parameters = "b, x";
	static constexpr const char *Description = "Computes the logarithm of x to base b. b may be omitted, in which case the default 10";
	static constexpr const char *Example = "log(2, 64)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct NextAfterFun {
	static constexpr const char *Name = "nextafter";
	static constexpr const char *Parameters = "x, y";
	static constexpr const char *Description = "Returns the next floating point value after x in the direction of y";
	static constexpr const char *Example = "nextafter(1::float, 2::float)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct PiFun {
	static constexpr const char *Name = "pi";
	static constexpr const char *Parameters = "";
	static constexpr const char *Description = "Returns the value of pi";
	static constexpr const char *Example = "pi()";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct RadiansFun {
	static constexpr const char *Name = "radians";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Converts degrees to radians";
	static constexpr const char *Example = "radians(90)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct RoundFun {
	static constexpr const char *Name = "round";
	static constexpr const char *Parameters = "x,precision";
	static constexpr const char *Description = "Rounds x to s decimal places";
	static constexpr const char *Example = "round(42.4332, 2)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct SignFun {
	static constexpr const char *Name = "sign";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Returns the sign of x as -1, 0 or 1";
	static constexpr const char *Example = "sign(-349)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct SignBitFun {
	static constexpr const char *Name = "signbit";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Returns whether the signbit is set or not";
	static constexpr const char *Example = "signbit(-0.0)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct SinFun {
	static constexpr const char *Name = "sin";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the sin of x";
	static constexpr const char *Example = "sin(90)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct SqrtFun {
	static constexpr const char *Name = "sqrt";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Returns the square root of x";
	static constexpr const char *Example = "sqrt(4)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct TanFun {
	static constexpr const char *Name = "tan";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the tan of x";
	static constexpr const char *Example = "tan(90)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct TruncFun {
	static constexpr const char *Name = "trunc";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Truncates the number";
	static constexpr const char *Example = "trunc(17.4)";
	static constexpr const char *Categories = "";

	static ScalarFunctionSet GetFunctions();
};

struct CoshFun {
	static constexpr const char *Name = "cosh";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the hyperbolic cos of x";
	static constexpr const char *Example = "cosh(1)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct SinhFun {
	static constexpr const char *Name = "sinh";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the hyperbolic sin of x";
	static constexpr const char *Example = "sinh(1)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct TanhFun {
	static constexpr const char *Name = "tanh";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the hyperbolic tan of x";
	static constexpr const char *Example = "tanh(1)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct AcoshFun {
	static constexpr const char *Name = "acosh";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the inverse hyperbolic cos of x";
	static constexpr const char *Example = "acosh(2.3)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct AsinhFun {
	static constexpr const char *Name = "asinh";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the inverse hyperbolic sin of x";
	static constexpr const char *Example = "asinh(0.5)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct AtanhFun {
	static constexpr const char *Name = "atanh";
	static constexpr const char *Parameters = "x";
	static constexpr const char *Description = "Computes the inverse hyperbolic tan of x";
	static constexpr const char *Example = "atanh(0.5)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

} // namespace duckdb
