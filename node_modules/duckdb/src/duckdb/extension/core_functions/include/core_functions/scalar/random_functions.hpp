//===----------------------------------------------------------------------===//
//                         DuckDB
//
// core_functions/scalar/random_functions.hpp
//
//
//===----------------------------------------------------------------------===//
// This file is automatically generated by scripts/generate_functions.py
// Do not edit this file manually, your changes will be overwritten
//===----------------------------------------------------------------------===//

#pragma once

#include "duckdb/function/function_set.hpp"

namespace duckdb {

struct RandomFun {
	static constexpr const char *Name = "random";
	static constexpr const char *Parameters = "";
	static constexpr const char *Description = "Returns a random number between 0 and 1";
	static constexpr const char *Example = "random()";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct SetseedFun {
	static constexpr const char *Name = "setseed";
	static constexpr const char *Parameters = "";
	static constexpr const char *Description = "Sets the seed to be used for the random function";
	static constexpr const char *Example = "setseed(0.42)";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct UUIDFun {
	static constexpr const char *Name = "uuid";
	static constexpr const char *Parameters = "";
	static constexpr const char *Description = "Returns a random UUID v4 similar to this: eeccb8c5-9943-b2bb-bb5e-222f4e14b687";
	static constexpr const char *Example = "uuid()";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct GenRandomUuidFun {
	using ALIAS = UUIDFun;

	static constexpr const char *Name = "gen_random_uuid";
};

struct UUIDv4Fun {
	static constexpr const char *Name = "uuidv4";
	static constexpr const char *Parameters = "";
	static constexpr const char *Description = "Returns a random UUIDv4 similar to this: eeccb8c5-9943-b2bb-bb5e-222f4e14b687";
	static constexpr const char *Example = "uuidv4()";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct UUIDv7Fun {
	static constexpr const char *Name = "uuidv7";
	static constexpr const char *Parameters = "";
	static constexpr const char *Description = "Returns a random UUID v7 similar to this: 019482e4-1441-7aad-8127-eec99573b0a0";
	static constexpr const char *Example = "uuidv7()";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct UUIDExtractVersionFun {
	static constexpr const char *Name = "uuid_extract_version";
	static constexpr const char *Parameters = "uuid";
	static constexpr const char *Description = "Extract a version for the given UUID.";
	static constexpr const char *Example = "uuid_extract_version('019482e4-1441-7aad-8127-eec99573b0a0')";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

struct UUIDExtractTimestampFun {
	static constexpr const char *Name = "uuid_extract_timestamp";
	static constexpr const char *Parameters = "uuid";
	static constexpr const char *Description = "Extract the timestamp for the given UUID v7.";
	static constexpr const char *Example = "uuid_extract_timestamp('019482e4-1441-7aad-8127-eec99573b0a0')";
	static constexpr const char *Categories = "";

	static ScalarFunction GetFunction();
};

} // namespace duckdb
