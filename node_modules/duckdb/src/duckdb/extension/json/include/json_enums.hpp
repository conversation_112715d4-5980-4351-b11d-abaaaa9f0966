//===----------------------------------------------------------------------===//
// This file is automatically generated by scripts/generate_enums.py
// Do not edit this file manually, your changes will be overwritten
//===----------------------------------------------------------------------===//

#pragma once

#include "duckdb/common/constants.hpp"
#include "duckdb/common/enum_util.hpp"

namespace duckdb {

enum class JSONScanType : uint8_t {
	INVALID = 0,
	//! Read JSON straight to columnar data
	READ_JSON = 1,
	//! Read JSON values as strings
	READ_JSON_OBJECTS = 2
};

enum class JSONRecordType : uint8_t {
	AUTO_DETECT = 0,
	//! Sequential objects that are unpacked
	RECORDS = 1,
	//! Any other JSON type, e.g., ARRAY
	VALUES = 2,
};

enum class JSONFormat : uint8_t {
	//! Auto-detect format (UNSTRUCTURED / NEWLINE_DELIMITED)
	AUTO_DETECT = 0,
	//! One unit after another, newlines can be anywhere
	UNSTRUCTURED = 1,
	//! Units are separated by newlines, newlines do not occur within Units (NDJSON)
	NEWLINE_DELIMITED = 2,
	//! File is one big array of units
	ARRAY = 3,
};

template<>
const char* EnumUtil::ToChars<JSONScanType>(JSONScanType value);

template<>
JSONScanType EnumUtil::FromString<JSONScanType>(const char *value);

template<>
const char* EnumUtil::ToChars<JSONRecordType>(JSONRecordType value);

template<>
JSONRecordType EnumUtil::FromString<JSONRecordType>(const char *value);

template<>
const char* EnumUtil::ToChars<JSONFormat>(JSONFormat value);

template<>
JSONFormat EnumUtil::FromString<JSONFormat>(const char *value);

} // namespace duckdb
